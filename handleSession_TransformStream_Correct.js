/**
 * handleSession函数 - 纯TransformStream链式版本
 * 基于ReadableStream_Chain的可用逻辑，使用纯链式TransformStream方式
 * 特点：ReadableStream → pipeThrough(TransformStream) → pipeTo(WritableStream)
 */
export async function handleSession(request, _env, _ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let headerDone = false;
    let tcpWriter = null;
    let tcpReader = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 纯链式：ReadableStream → TransformStream → WritableStream
    new ReadableStream({
        start(controller) {
            log('WebSocket ReadableStream启动');

            // 处理早期数据
            if (earlyHeader) {
                try {
                    const earlyData = decodeBase64Url(earlyHeader);
                    if (earlyData && earlyData.byteLength > 0) {
                        controller.enqueue(earlyData);
                        log(`处理早期数据: ${earlyData.byteLength} bytes`);
                    }
                } catch (e) {
                    log('早期数据解析失败', e.message);
                }
            }

            // WebSocket消息 → ReadableStream
            server.addEventListener("message", ({ data }) => {
                controller.enqueue(data);
            });

            // 关闭和错误处理
            server.addEventListener("close", () => {
                log('WebSocket连接关闭');
                controller.close();
            });

            server.addEventListener("error", (e) => {
                log('WebSocket错误', e);
                controller.error(e);
            });
        },
    })
    .pipeThrough(
        new TransformStream({
            async transform(chunk, controller) {
                try {
                    /* ---------- 首包：解析协议 + 建TCP ---------- */
                    if (!headerDone) {
                        headerDone = true;
                        log('开始处理协议头');

                        // 1) 解析协议头
                        const header = await parseProtocolHeader(chunk, server, protocolMode);

                        address = header.addressRemote;
                        portWithRandomLog = `${header.portRemote}--${Math.random()}`;
                        log(`协议头解析成功: ${address}:${header.portRemote}`);

                        // 2) 建立TCP连接（含重试）
                        let tcpInterface;
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                            log(`首次连接成功: ${globalControllerConfig.connectMode}`);
                        } catch (connectError) {
                            log(`首次连接失败: ${connectError.message}`);
                            try {
                                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                                log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                            } catch (retryError) {
                                log(`重试连接失败: ${retryError.message}`);
                                throw retryError;
                            }
                        }

                        // 3) 准备reader/writer
                        tcpWriter = tcpInterface.writable.getWriter();
                        tcpReader = tcpInterface.readable.getReader();

                        // 4) 发送握手剩余数据
                        if (header.rawClientData && header.rawClientData.byteLength > 0) {
                            await tcpWriter.write(header.rawClientData);
                            log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                        }

                        // 5) 单独异步任务：TCP → WebSocket
                        (async () => {
                            try {
                                log('开始TCP到WebSocket数据传输');
                                while (true) {
                                    const { value, done } = await tcpReader.read();
                                    if (done) {
                                        log('TCP数据读取完成');
                                        break;
                                    }
                                    if (value && value.byteLength > 0) {
                                        server.send(value);
                                        log(`发送TCP数据: ${value.byteLength} bytes`);
                                    }
                                }
                            } catch (e) {
                                log(`TCP到WebSocket传输错误: ${e.message}`);
                                try {
                                    server.close(1013, e.message);
                                } catch {}
                            } finally {
                                try {
                                    tcpReader.releaseLock();
                                } catch {}
                            }
                        })();

                        return; // 首包处理完成，不传递到下游
                    }

                    /* ---------- 后续数据：直接写TCP ---------- */
                    if (tcpWriter) {
                        await tcpWriter.write(chunk);
                        log(`写入TCP数据: ${chunk.byteLength} bytes`);
                    } else {
                        log('TCP连接未就绪');
                        throw new Error("TCP not ready");
                    }

                } catch (error) {
                    log(`Transform处理错误: ${error.message}`);
                    throw error;
                }
            },

            flush() {
                log('TransformStream结束');
                try {
                    tcpWriter?.close();
                } catch {}
            }
        })
    )
    .pipeTo(
        new WritableStream({
            write(chunk) {
                // 数据已在transform中处理完成，这里只是终点
                log('数据流终点');
            },
            close() {
                log('数据流完成');
            },
            abort(reason) {
                log('数据流中止', reason);
            }
        })
    )
    .catch(e => {
        log(`链式管道错误: ${e.message}`);
        try {
            server.close(1013, e.message);
        } catch {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig

/*
使用方式：
1. 这是一个纯链式TransformStream实现
2. ReadableStream → pipeThrough(TransformStream) → pipeTo(WritableStream)
3. 所有数据处理都在TransformStream的transform方法中完成
4. 比之前的版本更简洁，更符合Stream API的设计理念
5. 错误处理和资源清理都集成在链式调用中
*/
